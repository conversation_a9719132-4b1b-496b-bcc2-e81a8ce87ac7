import 'package:darve/utils/show_snackbar.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ProfileButtonsGroup extends StatelessWidget {
  final String userId;
  final String username;
  final String imageUrl;
  final bool isFollowing;
  final bool isFollowLoading;
  final bool isChatCreating;
  final VoidCallback onToggleFollow;
  final VoidCallback onCreateChat;

  const ProfileButtonsGroup({
    super.key,
    required this.userId,
    required this.username,
    required this.imageUrl,
    required this.isFollowing,
    required this.isFollowLoading,
    required this.isChatCreating,
    required this.onToggleFollow,
    required this.onCreateChat,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        OutlinedButton(
          onPressed: isFollowLoading ? null : onToggleFollow,
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: Colors.black),
            padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: isFollowLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                )
              : Text(
                  isFollowing ? 'Unfollow' : 'Follow',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                  ),
                ),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: isChatCreating ? null : onCreateChat,
          style: ElevatedButton.styleFrom(
            backgroundColor: Styles.primaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: isChatCreating
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text(
                  'Message',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
        ),
      ],
    );
  }
}


