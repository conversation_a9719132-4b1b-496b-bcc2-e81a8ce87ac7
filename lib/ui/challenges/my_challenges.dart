import 'package:darve/ui/components/challenges/my_challenge_card.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/api/models/challenge_model.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class MyChallenges extends StatefulWidget {
  const MyChallenges({super.key});

  @override
  State<MyChallenges> createState() => _MyChallengesState();
}

class _MyChallengesState extends State<MyChallenges> {
  List<ChallengeModel> currentChallenges = [];
  bool loaded = false;
  String selectedStatus = 'All'; // Initially set to 'All' for no filter.

  @override
  void initState() {
    super.initState();
    refetchChallenges();
  }

  void refetchChallenges() {
    ServiceProvider.challengeRepository.getMyChallenges().then((value) {
      setState(() {
        currentChallenges = value;
        loaded = true;
      });
    });
  }

  Future<Map<String, String>> fetchProfileData(String username) async {
    final profile = await ServiceProvider.profileRepository.getProfileData(username);
    return {
      'username': profile.userName!,
      'avatarUrl': ServerAssets().getAssetUrl(profile.imageUri!),
    };
  }

  List<ChallengeModel> filterChallenges() {
    if (selectedStatus == ChallengeType.all.value) {
      return currentChallenges;
    }
    return currentChallenges.where((challenge) {
      return challenge.status == selectedStatus;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'My Challenges',
          style: TextStyle(
            fontWeight: FontWeight.w400,
            color: Colors.black,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: DropdownButton<String>(
              value: selectedStatus,
              icon: const Icon(Icons.filter_list, color: Colors.black),
              dropdownColor: Colors.white,
              underline: const SizedBox(),
              items: [
                ChallengeType.all.value,
                ChallengeType.requested.value,
                ChallengeType.accepted.value,
                ChallengeType.rejected.value,
                ChallengeType.delivered.value,
              ].map((String status) {
                return DropdownMenuItem<String>(
                  value: status,
                  child: Text(status),
                );
              }).toList(),
              onChanged: (newValue) {
                setState(() {
                  selectedStatus = newValue!;
                });
              },
            ),
          ),
        ],
      ),
      body: Container(
        color: Colors.white,
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (filterChallenges().isEmpty && loaded)
              const Center(
                child: Text(
                  "No Challenges",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Styles.textLightColor,
                  ),
                ),
              ),
            Expanded(
              child: currentChallenges.isEmpty && !loaded
                  ? const Center(child: CircularProgressIndicator())
                  : ListView.builder(
                      itemCount: filterChallenges().length,
                      itemBuilder: (context, index) {
                        ChallengeModel challenge = filterChallenges()[index];
                        return FutureBuilder<Map<String, String>>(
                          future: fetchProfileData(challenge.fromUserId),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return Center(child: Container());
                            }
                            if (!snapshot.hasData || snapshot.hasError) {
                              return MyChallengeCard(
                                username: "Unknown",
                                avatarUrl: "",
                                challenge: challenge,
                                cb: refetchChallenges,
                              );
                            }

                            return MyChallengeCard(
                              username: snapshot.data!['username']!,
                              avatarUrl: snapshot.data!['avatarUrl']!,
                              challenge: challenge,
                              cb: refetchChallenges,
                              amount: challenge.participants.isNotEmpty
                                  ? challenge.participants[0].amount
                                  : 0,
                            );
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
