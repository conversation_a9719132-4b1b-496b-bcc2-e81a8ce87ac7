import 'package:darve/ui/components/common/search_field.dart';
import 'package:flutter/material.dart';
import '../components/settings/settings_menu_item.dart';

class PrivacySettings extends StatelessWidget {
  const PrivacySettings({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Settings',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Search Settings
          SearchField(
            onChanged: (value) {},
          ),

          // Heading
          const Padding(
            padding: EdgeInsets.fromLTRB(24, 8, 20, 8),
            child: Text(
              'Privacy and Security',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ),

          // Menu Items
          SettingsMenuItem(
            text: 'Change Password',
            onTap: () {
              // Handle change password
            },
          ),

          SettingsMenuItem(
            text: 'Two Factor Authentication',
            onTap: () {
              // Handle 2FA
            },
          ),

          SettingsMenuItem(
            text: 'Saved Login',
            onTap: () {
              // Handle saved login
            },
          ),

          SettingsMenuItem(
            text: 'Logged Devices',
            onTap: () {
              // Handle logged devices
            },
          ),

          SettingsMenuItem(
            text: 'Login Alert',
            onTap: () {
              // Handle login alert
            },
          ),
        ],
      ),
    );
  }
}
