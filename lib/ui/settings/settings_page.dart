import 'package:darve/ui/components/common/search_field.dart';
import 'package:darve/ui/auth/sigin/sign_in_controller.dart';
import 'package:darve/api/models/user_model.dart';
import 'package:darve/ui/settings/contact_us_support.dart';
import 'package:darve/ui/settings/more_info.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/request_cache.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../utils/styles.dart';
import '../components/settings/profile_card.dart';
import '../components/settings/settings_menu_item.dart';
import 'privacy_settings.dart';

class SettingsPage extends StatefulWidget {
  final VoidCallback? goBackToReelsCb;
  const SettingsPage({this.goBackToReelsCb, super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late final AuthService authService;

  @override
  void initState() {
    super.initState();
    authService = AuthProvider.auth;
  }

  UserModel? get user => authService.user;

  void _showLogoutModal() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color.fromARGB(255, 255, 255, 255),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          contentPadding: const EdgeInsets.all(36),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Log out of your account?',
                style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: Colors.black),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 34),
              GestureDetector(
                onTap: () async {
                  // Use new AuthController logout method
                  await authService.logout();

                  // Clear legacy user store and cache
                  await RequestCache.getInstance()
                      .then((value) => value.clearCache());
                },
                child: const Text(
                  'Log out',
                  style: TextStyle(
                      color: Colors.red,
                      fontSize: 18,
                      fontWeight: FontWeight.w500),
                ),
              ),
              const Divider(
                color: Styles.textLightColor,
                height: 20,
                thickness: 0.4,
              ),
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: const Text(
                  'Cancel',
                  style: TextStyle(
                      color: Colors.black,
                      fontSize: 18,
                      fontWeight: FontWeight.w400),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Settings',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
      ),
      body: Column(
        children: [
          SearchField(
            onChanged: (value) {},
          ),
          ProfileCard(
              avatarUrl: user?.imageUri?.toString() ?? '',
              name: user?.name ?? '',
              username: user?.username ?? ''),
          const SizedBox(height: 4),
          SettingsMenuItem(
            assetPath: 'assets/images/settings/privacy.png',
            text: 'Privacy and Security',
            onTap: () {
              RouteHelper.goToPrivacySettings();
            },
          ),
          SettingsMenuItem(
            assetPath: 'assets/images/settings/contact.png',
            text: 'Contact Us/Support',
            onTap: () {
              RouteHelper.goToContactSupport();
            },
          ),
          SettingsMenuItem(
            assetPath: 'assets/images/settings/info.png',
            text: 'More Info',
            onTap: () {
              RouteHelper.goToMoreInfo();
            },
          ),
          const Spacer(),
          Center(
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 0.35,
              height: 50,
              child: ElevatedButton(
                onPressed: _showLogoutModal,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Styles.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/settings/logout.png',
                      color: Colors.white,
                      height: 24,
                    ),
                    const Spacer(),
                    const Text(
                      'Logout',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const Spacer(),
          const Text(
            "Darve Mobile 1.0.1",
            style: TextStyle(fontSize: 12, color: Styles.textLightColor),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
