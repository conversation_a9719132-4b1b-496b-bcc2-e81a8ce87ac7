import 'package:darve/api/repositories/user_repository.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class VerifyEmailController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController confirmEmailController = TextEditingController();
  final RxBool isEmailValid = false.obs;
  final RxBool areEmailsMatching = false.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool isEmailChangedSuccess = false.obs;
  final RxString verificationCode = ''.obs;
  final RxBool isVerificationCodeSent = false.obs;
  final RxBool isVerificationCodeValid = false.obs;
  late final UserRepository _userRepository;

  @override
  void onInit() {
    super.onInit();
    _userRepository = ServiceProvider.userRepository;
  }

  @override
  void onClose() {
    emailController.dispose();
    confirmEmailController.dispose();
    super.onClose();
  }

  bool validateEmail(String email) {
    // Simple regex for email validation
    final RegExp emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  bool matchEmails() {
    return emailController.text.trim() == confirmEmailController.text.trim();
  }

  Future<void> verificationStart() async {
    if (!validateEmail(emailController.text.trim())) {
      errorMessage.value = 'Invalid email format';
      isLoading.value = false;
      return;
    }
    if (!matchEmails()) {
      errorMessage.value = 'Emails do not match';
      isLoading.value = false;
      return;
    }
    isLoading.value = true;
    try {
      await _userRepository.emailVerificationStart(emailController.text.trim());

      // Success
      isVerificationCodeSent.value = true;
      errorMessage.value = '';
    } catch (e) {
      // Handle specific error types
      errorMessage.value = _parseEmailVerificationError(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> verificationConfirm() async {
    if (verificationCode.value.isEmpty) {
      errorMessage.value = 'Verification code cannot be empty';
      return;
    }
    isLoading.value = true;
    try {
      await _userRepository.emailVerificationConfirm(
          emailController.text.trim(), verificationCode.value);

      // Success
      isVerificationCodeValid.value = true;
      isEmailChangedSuccess.value = true;
      errorMessage.value = '';
    } catch (e) {
      // Handle specific error types
      errorMessage.value = _parseEmailVerificationError(e);
    } finally {
      isLoading.value = false;
    }
  }

  // Parse email verification errors into user-friendly messages
  String _parseEmailVerificationError(dynamic error) {
    final errorString = error.toString();

    if (errorString.contains("invalid email") ||
        errorString.contains("email format")) {
      return 'Invalid email format';
    } else if (errorString.contains("invalid code") ||
               errorString.contains("expired code") ||
               errorString.contains("verification code")) {
      return 'Invalid or expired verification code';
    } else if (errorString.contains("already verified") ||
               errorString.contains("email verified")) {
      return 'Email is already verified';
    } else if (errorString.contains("User must be logged in")) {
      return 'Please log in to verify your email';
    } else if (errorString.contains("ClientException with SocketException") ||
               errorString.contains("network")) {
      return 'Network error. Please check your connection';
    } else {
      return 'Failed to verify email. Please try again';
    }
  }
}
