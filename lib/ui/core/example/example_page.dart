import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'example_controller.dart';

class ExamplePage extends GetView<ExampleController> {
  const ExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          EntityStateWidget(
            model: controller.profile,
          ),
          EntityStateWidget(
            model: controller.cars,
          ),
        ],
      ),
    );
  }
}
