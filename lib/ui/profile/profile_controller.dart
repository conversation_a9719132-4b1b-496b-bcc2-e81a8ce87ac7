import 'package:darve/api/models/user_model.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/api/models/follower_model.dart';
import 'package:darve/api/models/post_model.dart';
import 'package:darve/api/models/profile_data_model.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';

class ProfileController extends GetxController {
  late final AuthService authService;

  // ViewModel states for combined loading
  final Rx<ViewModel<ProfileDataModel>> profileState =
      const ViewModel<ProfileDataModel>.loading().obs;
  final Rx<ViewModel<List<PostModel>>> postsState =
      const ViewModel<List<PostModel>>.loading().obs;
  final Rx<ViewModel<List<FollowerModel>>> followersState =
      const ViewModel<List<FollowerModel>>.loading().obs;

  // Legacy data properties (for backward compatibility)
  RxBool isMe = true.obs;
  String userName;
  String userId;
  RxString content = "content".obs;

  // Action loading states (not part of combined loading)
  RxBool isFollowLoading = false.obs;
  RxBool isPostCreating = false.obs;
  RxBool isChatCreating = false.obs;

  // Follow state
  RxBool isFollowing = false.obs;

  // Error handling
  final ErrorsHandle _errorHandler = ErrorsHandle();

  ProfileController({required this.userName, required this.userId});

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
    isMe.value = user?.id == userId;
    // Auto-load profile data when controller is initialized
    loadProfileData();
  }

  @override
  void onClose() {
    // Clean up any subscriptions or resources
    super.onClose();
  }

  UserModel? get user => authService.user;

  // Convenience getters for accessing data from ViewModels
  ProfileDataModel get userProfile => profileState.value.data ?? ProfileDataModel.empty();
  List<PostModel> get posts => postsState.value.data ?? [];
  List<FollowerModel> get followers => followersState.value.data ?? [];

  /// Load complete profile data including profile info, posts, and followers
  Future<bool> loadProfileData() async {
    // Set all states to loading
    profileState.value = const ViewModel<ProfileDataModel>.loading();
    postsState.value = const ViewModel<List<PostModel>>.loading();
    followersState.value = const ViewModel<List<FollowerModel>>.loading();

    // Load all data concurrently
    await Future.wait([
      _loadProfile(),
      _loadPosts(),
      _loadFollowers(),
      if (!isMe.value) _checkFollowingStatus(),
    ]);

    // Return true if at least profile data loaded successfully
    return profileState.value.state == ViewState.content;
  }

  /// Load profile data
  Future<void> _loadProfile() async {
    try {
      final profileData = await ServiceProvider.profileRepository.getProfileData(userName);
      profileState.value = ViewModel<ProfileDataModel>.content(profileData);
    } catch (e) {
      profileState.value = ViewModel<ProfileDataModel>.error(e);
      _errorHandler.displayErrorToast(e, 'loadProfile');
    }
  }

  /// Load user posts
  Future<void> _loadPosts() async {
    try {
      final userPosts = await ServiceProvider.postsRepository.getPosts(userName);
      if (userPosts.isEmpty) {
        postsState.value = const ViewModel<List<PostModel>>.empty();
      } else {
        postsState.value = ViewModel<List<PostModel>>.content(userPosts);
      }
    } catch (e) {
      postsState.value = ViewModel<List<PostModel>>.error(e);
      _errorHandler.displayErrorToast(e, 'loadPosts');
    }
  }

  /// Load followers list
  Future<void> _loadFollowers() async {
    try {
      final followersList = await ServiceProvider.profileRepository.getFollowers(userId);
      if (followersList.isEmpty) {
        followersState.value = const ViewModel<List<FollowerModel>>.empty();
      } else {
        followersState.value = ViewModel<List<FollowerModel>>.content(followersList);
      }
    } catch (e) {
      followersState.value = ViewModel<List<FollowerModel>>.error(e);
      _errorHandler.displayErrorToast(e, 'loadFollowers');
    }
  }

  /// Check if current user is following this profile
  Future<void> _checkFollowingStatus() async {
    try {
      final followingStatus = await ServiceProvider.profileRepository.isFollowing(userId);
      isFollowing.value = followingStatus;
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'checkFollowingStatus');
    }
  }

  /// Reload profile data (public method for UI)
  Future<bool> profileDataReload() async {
    return await loadProfileData();
  }

  /// Follow or unfollow user
  Future<void> toggleFollow() async {
    if (isFollowLoading.value) return;

    try {
      isFollowLoading.value = true;

      if (isFollowing.value) {
        await ServiceProvider.profileRepository.unfollowUser(userId);
        isFollowing.value = false;
      } else {
        await ServiceProvider.profileRepository.followUser(userId);
        isFollowing.value = true;
      }

      // Reload profile data to update follower counts
      await loadProfileData();

    } catch (e) {
      _errorHandler.displayErrorToast(e, 'toggleFollow');
    } finally {
      isFollowLoading.value = false;
    }
  }

  /// Create a new post
  Future<void> createPost({String? filePath}) async {
    if (isPostCreating.value) return;

    try {
      isPostCreating.value = true;

      String? selectedFilePath = filePath;

      // If no file path provided, let user pick one
      if (selectedFilePath == null) {
        final result = await FilePicker.platform.pickFiles(
          type: FileType.image,
        );

        if (result != null && result.files.single.path != null) {
          selectedFilePath = result.files.single.path!;
        } else {
          return; // User cancelled file selection
        }
      }

      // Create the post
      await ServiceProvider.postsRepository.createPost(
        content.value,
        filePath: selectedFilePath,
      );

      // Reload posts to show the new post
      await _loadPosts();

    } catch (e) {
      _errorHandler.displayErrorToast(e, 'createPost');
    } finally {
      isPostCreating.value = false;
    }
  }

  /// Create chat with this user
  Future<String?> createChatWithUser() async {
    if (isChatCreating.value) return null;

    try {
      isChatCreating.value = true;

      final chatData = await ServiceProvider.chatRepository.createChatWithUserId(userId);

      if (chatData != null && chatData['discussion_id'] != null) {
        final discussionId = chatData['discussion_id'] as String;
        RouteHelper.goToChat(
          chatId: discussionId,
          title: userName,
          avatarUrl: userProfile.imageUri ?? '',
          userId: userId,
        );
        return chatData['discussion_id'] as String;
      }

      return null;
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'createChatWithUser');
      return null;
    } finally {
      isChatCreating.value = false;
    }
  }

  /// Legacy method for backward compatibility
  Future<void> makePost(String? pickedFilePath) async {
    await createPost(filePath: pickedFilePath);
  }
}
