import 'package:darve/api/models/post_model.dart';
import 'package:darve/api/models/follower_model.dart';
import 'package:darve/ui/components/profile/profile_buttons_group.dart';
import 'package:darve/ui/components/profile/create_post_btn.dart';
import 'package:darve/ui/components/profile/social_group.dart';
import 'package:darve/ui/components/profile/user_aggregate.dart';
import 'package:darve/ui/components/profile/post_modal.dart';
import 'package:darve/ui/components/profile/top_bar.dart';
import 'package:darve/ui/core/combined_loading_state_widget.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/show_snackbar.dart';
import 'package:darve/ui/profile/profile_controller.dart';
import 'package:darve/utils/medialinks.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../components/profile/post_card.dart';
import '../components/common/linear_card.dart';
import 'package:sliding_up_panel2/sliding_up_panel2.dart';

class ProfilePage extends GetView<ProfileController> {
  final String userId;
  final String username;
  final String imageUrl;
  final String fullName;
  final VoidCallback? goBackToReels;
  final ScrollController _scrollController = ScrollController();

  ProfilePage(
    this.userId,
    this.username,
    this.imageUrl,
    this.fullName, {
    this.goBackToReels,
    super.key,
  });

  @override
  String? get tag => userId; // Use userId as tag to get the correct controller instance

  void _showPostModal(BuildContext context) {
    PostModal(
      (pickedFilePath, content) async {
        if (content.isEmpty || pickedFilePath.isEmpty) {
          return;
        }
        Navigator.of(context).pop();
        controller.content.value = content;
        await controller.createPost(filePath: pickedFilePath);
        pickedFilePath = "";
      },
      (content) {
        controller.content.value = content;
      },
      '',
    ).showPostModal(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SlidingUpPanel(
          maxHeight: MediaQuery.of(context).size.height * 0.80,
          minHeight: MediaQuery.of(context).size.height *
              (controller.isMe.value ? 0.50 : 0.55),
          body: Stack(
            children: [
              if (imageUrl.isNotEmpty)
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Image.network(
                    ServerAssets().getAssetUrl(imageUrl),
                    height: 450,
                    fit: BoxFit.cover,
                  ),
                ),
              Obx(() {
                return TopBar(
                    title: "Profile",
                    isTransparent: true,
                    isCurrentUser: controller.isMe.value,
                    callback: goBackToReels);
              })
            ],
          ),
          scrollController: _scrollController,
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(40.0), topRight: Radius.circular(40.0)),
          panelBuilder: () => SizedBox(
                height: double.infinity,
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: CombinedLoadingStateWidget(
                        viewModels: [
                          controller.profileState,
                          controller.postsState,
                          controller.followersState,
                        ],
                        loadingMessage: "Loading profile data...",
                        child: Column(
                          children: [
                            Text(
                              fullName,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 2),
                            if (controller.userProfile.bio!.isNotEmpty)
                              SizedBox(
                                width: MediaQuery.of(context).size.width * 0.8,
                                child: Text(
                                  controller.userProfile.bio!,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            const SizedBox(height: 16),
                            SocialGroup(
                                controller.userProfile.socialLinks!,
                                controller.userProfile.platforms),
                            const SizedBox(height: 24),
                            UserAggregate(
                                controller.userProfile.followingNr
                                    .toString(),
                                controller.userProfile.followersNr
                                    .toString(),
                                controller.posts,
                                userId),
                            if (!controller.isMe.value)
                              const SizedBox(height: 24),
                            if (!controller.isMe.value)
                              Obx(() => ProfileButtonsGroup(
                                userId: userId,
                                username: username,
                                imageUrl: imageUrl,
                                isFollowing: controller.isFollowing.value,
                                isFollowLoading: controller.isFollowLoading.value,
                                isChatCreating: controller.isChatCreating.value,
                                onToggleFollow: () async {
                                  await controller.toggleFollow();
                                  // Show snackbar feedback
                                  SnackbarHelper.showFollowSnackbar(
                                    context: context,
                                    imageUri: imageUrl,
                                    username: username,
                                    bgColor: controller.isFollowing.value
                                        ? Colors.green
                                        : Colors.red,
                                    isFollowed: controller.isFollowing.value,
                                  );
                                },
                                onCreateChat: controller.createChatWithUser,
                              )),
                            const SizedBox(height: 24),
                            // ElevatedButton(
                            //   onPressed: handlePostButtonClick,
                            //   child: Text("Post"),
                            // ),
                            SizedBox(
                              height: 500,
                              child: DefaultTabController(
                                length: 2,
                                child: Builder(
                                  builder: (context) {
                                    final TabController tabController =
                                        DefaultTabController.of(context);
                                    tabController.addListener(() {
                                      // setState(() {});
                                    });

                                    return Column(
                                      children: [
                                        TabBar(
                                          indicatorColor: Colors.black,
                                          indicatorWeight: 1,
                                          indicatorSize: TabBarIndicatorSize.tab,
                                          tabs: [
                                            Tab(
                                              icon: Image.asset(
                                                  'assets/images/profile/feed.png',
                                                  color: Colors.black,
                                                  width: 25,
                                                  height: 25,
                                                  semanticLabel: 'Tab 1'),
                                            ),
                                            Tab(
                                              icon: Image.asset(
                                                  'assets/images/profile/people.png',
                                                  color: Colors.black,
                                                  width: 25,
                                                  height: 25,
                                                  semanticLabel: 'Tab 2'),
                                            ),
                                          ],
                                        ),
                                        Expanded(
                                          child: TabBarView(
                                            children: [
                                              EntityStateWidget<List<PostModel>>(
                                                model: controller.postsState,
                                                onRetry: () => controller.reloadPosts(),
                                                emptyMessage: controller.isMe.value
                                                    ? "Create your first post!"
                                                    : "No Posts yet!",
                                                emptyIcon: const Icon(
                                                  Icons.post_add_outlined,
                                                  size: 64,
                                                  color: Colors.grey,
                                                ),
                                                emptyBuilder: controller.isMe.value
                                                    ? (message) => SizedBox(
                                                        height: 180,
                                                        width: 120,
                                                        child: Obx(() => CreatePostBtn(
                                                            _showPostModal,
                                                            isPostCreating: controller.isPostCreating.value)))
                                                    : null,
                                                itemBuilder: (posts) => SingleChildScrollView(
                                                  physics: const AlwaysScrollableScrollPhysics(),
                                                  child: Column(
                                                    children: [
                                                      GridView.builder(
                                                        physics: const NeverScrollableScrollPhysics(),
                                                        shrinkWrap: true,
                                                        padding: const EdgeInsets.all(8),
                                                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                                          crossAxisCount: 3,
                                                          crossAxisSpacing: 2,
                                                          mainAxisSpacing: 8,
                                                          childAspectRatio: 0.6,
                                                        ),
                                                        itemCount: posts.length + (controller.isMe.value ? 1 : 0),
                                                        itemBuilder: (context, index) {
                                                          if (controller.isMe.value && index == 0) {
                                                            return Obx(() => CreatePostBtn(
                                                                _showPostModal,
                                                                isPostCreating: controller.isPostCreating.value));
                                                          } else {
                                                            final postIndex = controller.isMe.value ? index - 1 : index;
                                                            return PostCard(
                                                              post: posts[postIndex],
                                                              imageUrl: MediaLinksHelper().getPostReel(posts[postIndex]),
                                                            );
                                                          }
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              EntityStateWidget<List<FollowerModel>>(
                                                model: controller.followersState,
                                                onRetry: () => controller.reloadFollowers(),
                                                emptyMessage: "No followers yet",
                                                emptyIcon: const Icon(
                                                  Icons.people_outline,
                                                  size: 64,
                                                  color: Colors.grey,
                                                ),
                                                itemBuilder: (followers) => ListView.builder(
                                                  physics: const AlwaysScrollableScrollPhysics(),
                                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                                  itemCount: followers.length,
                                                  itemBuilder: (context, index) {
                                                    return LinearCard(
                                                      userName: followers[index].username,
                                                      imageUrl: followers[index].imageUrl,
                                                    );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              )),
    );
  }
}
