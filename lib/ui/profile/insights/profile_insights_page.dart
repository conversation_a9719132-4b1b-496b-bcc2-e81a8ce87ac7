import 'package:darve/ui/profile/insights/profile_insights_controller.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/api/models/follower_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../components/profile/profile_card.dart';

class ProfileInsightsPage extends GetView<ProfileInsightsController> {
  final String userId;
  final int initialTab;

  const ProfileInsightsPage({
    super.key,
    required this.userId,
    this.initialTab = 0,
  });

  @override
  String? get tag => 'profile_insights_$userId';

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: initialTab,
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'Profile Insights',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
              fontSize: 22,
            ),
          ),
          backgroundColor: Colors.white,
          elevation: 0,
          bottom: const TabBar(
            indicatorSize: TabBarIndicatorSize.tab,
            labelColor: Colors.black,
            unselectedLabelColor: Colors.grey,
            tabs: [
              Tab(text: 'Followers'),
              Tab(text: 'Following'),
            ],
          ),
        ),
        body: Container(
          color: Colors.white,
          child: TabBarView(
            children: [
              // Followers Tab
              EntityStateWidget<List<FollowerModel>>(
                model: controller.followersState,
                onRetry: () => controller.loadFollowers(),
                emptyMessage: "No Followers yet",
                emptyIcon: const Icon(
                  Icons.people_outline,
                  size: 64,
                  color: Colors.grey,
                ),
                itemBuilder: (followers) => _buildFollowersGrid(followers),
              ),

              // Following Tab
              EntityStateWidget<List<FollowerModel>>(
                model: controller.followingState,
                onRetry: () => controller.loadFollowing(),
                emptyMessage: "User not following anyone.",
                emptyIcon: const Icon(
                  Icons.person_add_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                itemBuilder: (following) => _buildFollowingGrid(following),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build followers grid view
  Widget _buildFollowersGrid(List<FollowerModel> followers) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: followers.length,
      itemBuilder: (context, index) {
        final follower = followers[index];
        return ProfileCard(
          userName: follower.username,
          subText: 'Followed by Alex and ${index + 3} others',
          imageUrl: follower.imageUrl,
          btnVal: 'Remove',
          onBtnClick: () {
            // TODO: Implement remove follower functionality
            // This would require a new API endpoint
          },
        );
      },
    );
  }

  /// Build following grid view
  Widget _buildFollowingGrid(List<FollowerModel> following) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: following.length,
      itemBuilder: (context, index) {
        final followingUser = following[index];
        return ProfileCard(
          userName: followingUser.username,
          subText: 'Followed by Sarah and ${index + 2} others',
          imageUrl: followingUser.imageUrl,
          btnVal: 'Following',
          onBtnClick: () async {
            await controller.unfollowUser(followingUser, context);
          },
        );
      },
    );
  }
}
