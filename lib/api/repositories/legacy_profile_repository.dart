import 'package:darve/api/models/follower_model.dart';
import 'package:darve/api/models/profile_data_model.dart';
import 'package:darve/services/http/http_service.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/id_extractor.dart';

/// Legacy ProfileRepository that throws exceptions
///
/// This class maintains backward compatibility for existing code that uses
/// try-catch blocks for error handling. New code should use the main
/// ProfileRepository with Result types instead.
///
/// @deprecated Use ProfileRepository with Result-based methods instead
@Deprecated(
    'Use ProfileRepository with Result-based methods instead. This class will be removed in a future version.')
class LegacyProfileRepository {
  final HttpService _dioService;

  LegacyProfileRepository(this._dioService);

  /// Get profile data - throws exceptions on error
  ///
  /// @deprecated Use ProfileRepository.getProfileDataResult() instead
  Future<ProfileDataModel> getProfileData(String username) async {
    try {
      if (username.startsWith("@")) {
        username = username.split("@")[1];
      }

      final response = await _dioService.get(ApiPaths.userProfile(username));

      if (response.data != null) {
        return ProfileDataModel.fromJson(response.data);
      }
      return ProfileDataModel.empty();
    } catch (e) {
      // If it's already an AppError (from HTTP interceptor), re-throw it
      if (e is AppError) {
        rethrow;
      }

      // For unexpected errors, wrap in UserError with context
      throw UserError(
        message: 'Unable to load profile data. Please try again.',
        code: 'PROFILE_LOAD_ERROR',
        originalError: e,
        metadata: {
          'username': username,
          'operation': 'getProfileData',
        },
      );
    }
  }

  /// Get followers list - throws exceptions on error
  ///
  /// @deprecated Use ProfileRepository.getFollowersResult() instead
  Future<List<FollowerModel>> getFollowers(String userId) async {
    try {
      final response = await _dioService.get(ApiPaths.getFollowers(userId));

      if (response.data == null || response.data['items'] == null) {
        return [];
      }

      // Parse followers list from response
      List<FollowerModel> followers = (response.data['items'] as List)
          .map((item) => FollowerModel(
                id: IdExtractor.getIdent(item),
                username: item['username'] ?? '',
                name: item['name'] ?? '',
                imageUrl: item['image_url'] ?? '',
              ))
          .toList();
      return followers;
    } catch (e) {
      // If it's already an AppError (from HTTP interceptor), re-throw it
      if (e is AppError) {
        rethrow;
      }

      // For unexpected errors, wrap in UserError with context
      throw UserError(
        message: 'Unable to load followers. Please try again.',
        code: 'FOLLOWERS_LOAD_ERROR',
        originalError: e,
        metadata: {
          'userId': userId,
          'operation': 'getFollowers',
        },
      );
    }
  }

  /// Get following list - throws exceptions on error
  ///
  /// @deprecated Use ProfileRepository.getFollowingResult() instead
  Future<List<FollowerModel>> getFollowing(String userId) async {
    try {
      final response = await _dioService.get(ApiPaths.getFollowing(userId));

      if (response.data == null || response.data['items'] == null) {
        return [];
      }

      // Parse following list from response
      List<FollowerModel> following = (response.data['items'] as List)
          .map((item) => FollowerModel(
                id: IdExtractor.getIdent(item),
                username: item['username'] ?? '',
                name: item['name'] ?? '',
                imageUrl: item['image_url'] ?? '',
              ))
          .toList();
      return following;
    } catch (e) {
      // If it's already an AppError (from HTTP interceptor), re-throw it
      if (e is AppError) {
        rethrow;
      }

      // For unexpected errors, wrap in UserError with context
      throw UserError(
        message: 'Unable to load following list. Please try again.',
        code: 'FOLLOWING_LOAD_ERROR',
        originalError: e,
        metadata: {
          'userId': userId,
          'operation': 'getFollowing',
        },
      );
    }
  }

  /// Unfollow user - throws exceptions on error
  ///
  /// @deprecated Use ProfileRepository.unfollowUserResult() instead
  Future<dynamic> unfollowUser(String userId) async {
    try {
      final response = await _dioService.delete(ApiPaths.unfollowUser(userId));
      return response.data;
    } catch (e) {
      // If it's already an AppError (from HTTP interceptor), re-throw it
      if (e is AppError) {
        rethrow;
      }

      // For unexpected errors, wrap in UserError with context
      throw UserError(
        message: 'Unable to unfollow user. Please try again.',
        code: 'UNFOLLOW_ERROR',
        originalError: e,
        metadata: {
          'userId': userId,
          'operation': 'unfollowUser',
        },
      );
    }
  }

  /// Search user - throws exceptions on error
  ///
  /// @deprecated Use ProfileRepository.searchUserResult() instead (when available)
  Future<dynamic> searchUser(String userInput) async {
    final response = await _dioService.post(
      ApiPaths.searchUser,
      data: {'query': userInput},
    );

    return response.data['items'] ?? [];
  }

  /// Check if following user - throws exceptions on error
  ///
  /// @deprecated Use ProfileRepository.isFollowingResult() instead (when available)
  Future<bool> isFollowing(String userId) async {
    final response = await _dioService.get(ApiPaths.isFollowing(userId));
    return response.data['is_following'] ?? false;
  }

  /// Edit profile - throws exceptions on error
  ///
  /// @deprecated Use ProfileRepository.editProfileResult() instead (when available)
  Future<dynamic> editProfile(String profileData, String imagePath) async {
    final response = await _dioService.post(
      ApiPaths.editProfile,
      data: profileData,
    );
    return response.data;
  }

  /// Follow user - throws exceptions on error
  ///
  /// @deprecated Use ProfileRepository.followUserResult() instead (when available)
  Future<dynamic> followUser(String userId) async {
    final response = await _dioService.post(ApiPaths.followUser(userId));
    return response.data;
  }
}
