import 'package:darve/api/models/follower_model.dart';
import 'package:darve/api/models/profile_data_model.dart';
import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/id_extractor.dart';
import 'package:darve/utils/result.dart';

class ProfileRepository {
  final HttpService _dioService;

  ProfileRepository(this._dioService);

  /// Get profile data using Result type for better error handling
  Future<Result<ProfileDataModel>> getProfileData(String username) async {
    return ResultHelper.tryCallAsync(
      () async {
        if (username.startsWith("@")) {
          username = username.split("@")[1];
        }

        final response = await _dioService.get(ApiPaths.userProfile(username));

        if (response.data != null) {
          return ProfileDataModel.fromJson(response.data);
        }
        return ProfileDataModel.empty();
      },
      errorMessage: 'Unable to load profile data. Please try again.',
      errorCode: 'PROFILE_LOAD_ERROR',
      metadata: {
        'username': username,
        'operation': 'getProfileData',
      },
    );
  }

  /// Get followers using Result type for better error handling
  Future<Result<List<FollowerModel>>> getFollowers(String userId) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.get(ApiPaths.getFollowers(userId));

        if (response.data == null || response.data['items'] == null) {
          return <FollowerModel>[];
        }

        // Parse followers list from response
        List<FollowerModel> followers = (response.data['items'] as List)
            .map((item) => FollowerModel(
                  id: IdExtractor.getIdent(item),
                  username: item['username'] ?? '',
                  name: item['name'] ?? '',
                  imageUrl: item['image_url'] ?? '',
                ))
            .toList();
        return followers;
      },
      errorMessage: 'Unable to load followers. Please try again.',
      errorCode: 'FOLLOWERS_LOAD_ERROR',
      metadata: {
        'userId': userId,
        'operation': 'getFollowers',
      },
    );
  }

  /// Get following list using Result type for better error handling
  Future<Result<List<FollowerModel>>> getFollowing(String userId) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.get(ApiPaths.getFollowing(userId));

        if (response.data == null || response.data['items'] == null) {
          return <FollowerModel>[];
        }

        // Parse following list from response
        List<FollowerModel> following = (response.data['items'] as List)
            .map((item) => FollowerModel(
                  id: IdExtractor.getIdent(item),
                  username: item['username'] ?? '',
                  name: item['name'] ?? '',
                  imageUrl: item['image_url'] ?? '',
                ))
            .toList();
        return following;
      },
      errorMessage: 'Unable to load following list. Please try again.',
      errorCode: 'FOLLOWING_LOAD_ERROR',
      metadata: {
        'userId': userId,
        'operation': 'getFollowing',
      },
    );
  }

  /// Unfollow user using Result type for better error handling
  Future<Result<dynamic>> unfollowUser(String userId) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.delete(ApiPaths.unfollowUser(userId));
        return response.data;
      },
      errorMessage: 'Unable to unfollow user. Please try again.',
      errorCode: 'UNFOLLOW_ERROR',
      metadata: {
        'userId': userId,
        'operation': 'unfollowUser',
      },
    );
  }

  /// Search user using Result type for better error handling
  Future<Result<List<dynamic>>> searchUser(String userInput) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.post(
          ApiPaths.searchUser,
          data: {'query': userInput},
        );
        return response.data['items'] ?? [];
      },
      errorMessage: 'Unable to search users. Please try again.',
      errorCode: 'SEARCH_USER_ERROR',
      metadata: {
        'query': userInput,
        'operation': 'searchUser',
      },
    );
  }

  /// Check if following user using Result type for better error handling
  Future<Result<bool>> isFollowing(String userId) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.get(ApiPaths.isFollowing(userId));
        return response.data['is_following'] ?? false;
      },
      errorMessage: 'Unable to check following status. Please try again.',
      errorCode: 'IS_FOLLOWING_ERROR',
      metadata: {
        'userId': userId,
        'operation': 'isFollowing',
      },
    );
  }

  /// Edit profile using Result type for better error handling
  Future<Result<dynamic>> editProfile(
      String profileData, String imagePath) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.post(
          ApiPaths.editProfile,
          data: profileData,
        );
        return response.data;
      },
      errorMessage: 'Unable to edit profile. Please try again.',
      errorCode: 'EDIT_PROFILE_ERROR',
      metadata: {
        'operation': 'editProfile',
      },
    );
  }

  /// Follow user using Result type for better error handling
  Future<Result<dynamic>> followUser(String userId) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.post(ApiPaths.followUser(userId));
        return response.data;
      },
      errorMessage: 'Unable to follow user. Please try again.',
      errorCode: 'FOLLOW_USER_ERROR',
      metadata: {
        'userId': userId,
        'operation': 'followUser',
      },
    );
  }
}
