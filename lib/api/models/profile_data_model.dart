import 'package:darve/utils/parsers/social_parse.dart';

class ProfileDataModel {
  final String? fullName;
  final String? userId;
  final String? userName;
  final String? emailId;
  final String? bio;
  final String? imageUri;
  final int followersNr;
  final int followingNr;
  final List<String> platforms;
  final List<String>? socialLinks;

  ProfileDataModel({
    required this.fullName,
    required this.userId,
    required this.userName,
    required this.emailId,
    required this.bio,
    required this.imageUri,
    required this.followersNr,
    required this.followingNr,
    required this.platforms,
    required this.socialLinks,
  });

  factory ProfileDataModel.fromJson(Map<String, dynamic> json) {
    return ProfileDataModel(
      fullName: json['profile_view']['full_name'],
      userId: json['profile_view']['user_id'] != null
          ? "${json['profile_view']['user_id']['tb']}:${json['profile_view']['user_id']['id']['String']}"
          : null,
      userName: json['profile_view']['username'],
      emailId: json['profile_view']['emailId'],
      bio: json['profile_view']['bio'] ?? "",
      imageUri: json['profile_view']['image_uri'],
      followersNr: json['profile_view']['followers_nr'] ?? 0,
      followingNr: json['profile_view']['following_nr'] ?? 0,
      platforms:
          SocialParse().parseSocialLinks(json['profile_view']['social_links']),
      socialLinks: json['profile_view']['social_links'] ?? [],
    );
  }

  factory ProfileDataModel.empty() {
    return ProfileDataModel(
      fullName: null,
      userId: null,
      userName: null,
      emailId: null,
      bio: '',
      imageUri: null,
      followersNr: 0,
      followingNr: 0,
      platforms: SocialParse().parseSocialLinks([]),
      socialLinks: [],
    );
  }
}
